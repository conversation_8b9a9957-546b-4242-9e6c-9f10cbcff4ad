from fastapi import APIRouter, Request, Depends, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session
from app.database import get_db
from app.dependencies import require_auth, get_current_user
from app.models import User
import logging

logger = logging.getLogger(__name__)
router = APIRouter()
templates = Jinja2Templates(directory="templates")

@router.get("/profile", response_class=HTMLResponse)
async def user_profile(
    request: Request,
    user: User = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """Show user profile page."""
    return templates.TemplateResponse("profile/index.html", {
        "request": request,
        "user": user,
        "repository": user.repository
    })

@router.get("/.profile", response_class=HTMLResponse)
async def user_profile_alt(
    request: Request,
    user: User = Depends(require_auth),
    db: Session = Depends(get_db)
):
    """Alternative profile endpoint with dot prefix."""
    return templates.TemplateResponse("profile/index.html", {
        "request": request,
        "user": user,
        "repository": user.repository
    })

@router.get("/{username}", response_class=HTMLResponse)
async def user_public_profile(
    username: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """Show public user profile by username."""
    # Get user by username
    user = db.query(User).filter(User.username == username).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Get current user (if logged in)
    current_user = get_current_user(request, db)
    
    return templates.TemplateResponse("profile/public.html", {
        "request": request,
        "profile_user": user,
        "current_user": current_user,
        "repository": user.repository
    })

@router.get("/{username}/{repository_name}", response_class=HTMLResponse)
async def user_repository_view(
    username: str,
    repository_name: str,
    request: Request,
    path: str = "/",
    db: Session = Depends(get_db)
):
    """Show user's repository in GitHub-style URL pattern."""
    # Get user by username
    user = db.query(User).filter(User.username == username).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Check if user has a repository and if the name matches
    if not user.repository:
        raise HTTPException(status_code=404, detail="Repository not found")
    
    # Check if repository name matches (case-insensitive)
    if user.repository.name.lower() != repository_name.lower():
        raise HTTPException(status_code=404, detail="Repository not found")
    
    # Get current user (if logged in)
    current_user = get_current_user(request, db)
    
    # Import filesystem service here to avoid circular imports
    from app.services.filesystem_service import FileSystemService
    from app.utils.helpers import format_file_size, get_file_icon
    from datetime import datetime
    
    fs_service = FileSystemService()
    
    try:
        contents = fs_service.get_directory_contents(db, user.repository, path)
        breadcrumbs = get_path_segments(path)
        
        return templates.TemplateResponse("repository/public.html", {
            "request": request,
            "profile_user": user,
            "current_user": current_user,
            "repository": user.repository,
            "current_path": path,
            "contents": contents,
            "breadcrumbs": breadcrumbs,
            "now": datetime.now(),
            "format_file_size": format_file_size,
            "get_file_icon": get_file_icon
        })
        
    except Exception as e:
        logger.error(f"Error loading repository contents: {e}")
        return templates.TemplateResponse("repository/public.html", {
            "request": request,
            "profile_user": user,
            "current_user": current_user,
            "repository": user.repository,
            "current_path": path,
            "contents": [],
            "breadcrumbs": [],
            "error": "Failed to load repository contents",
            "now": datetime.now(),
            "format_file_size": format_file_size,
            "get_file_icon": get_file_icon
        })

def get_path_segments(path: str):
    """Get path segments for breadcrumb navigation."""
    if path == "/" or not path:
        return [{"name": "Root", "path": "/"}]
    
    segments = [{"name": "Root", "path": "/"}]
    parts = [p for p in path.split("/") if p]
    
    current_path = ""
    for part in parts:
        current_path += "/" + part
        segments.append({"name": part, "path": current_path})
    
    return segments
