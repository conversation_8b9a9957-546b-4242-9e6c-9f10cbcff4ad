<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ profile_user.username }}/{{ repository.name }} - WastedTime</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        custom: {
                            'bg': '#1a1c1c',
                            'surface': '#181a1b'
                        },
                        dark: {
                            '50': '#f6f6f7',
                            '100': '#e1e3e6',
                            '200': '#c2c5cb',
                            '300': '#9ba1ab',
                            '400': '#767d8a',
                            '500': '#5c636f',
                            '600': '#3a404a',
                            '700': '#2a2f38',
                            '800': '#1c2028',
                            '900': '#12151c',
                            '950': '#0a0c10',
                        }
                    }
                }
            }
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Loading spinner */
        .loading-spinner {
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            border-top: 3px solid #3b82f6;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50 dark:bg-custom-bg transition-colors duration-200">
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
        <div class="loading-spinner dark:border-dark-600 dark:border-t-blue-500"></div>
    </div>

    <div class="bg-white dark:bg-custom-surface shadow-sm sticky top-0 z-20 transition-colors duration-200">
        <div class="container mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <h1 class="text-2xl font-semibold text-gray-900 dark:text-gray-100 flex items-center">
                        <i class="far fa-code mr-2 text-gray-600 dark:text-gray-400"></i>
                        <a href="/{{ profile_user.username }}" class="hover:text-blue-600 dark:hover:text-blue-400">{{ profile_user.username }}</a>
                        <span class="mx-2 text-gray-500">/</span>
                        <span class="font-bold">{{ repository.name }}</span>
                        <span class="ml-2 text-xs px-2 py-0.5 rounded-full bg-gray-100 dark:bg-dark-800 text-gray-600 dark:text-gray-300 font-normal">
                            Public
                        </span>
                    </h1>
                </div>
                <div class="flex items-center space-x-4 text-sm">
                    <button id="darkModeToggle" class="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-dark-800 transition-colors duration-200">
                        <i class="far fa-moon dark:hidden text-gray-600"></i>
                        <i class="far fa-sun hidden dark:inline text-gray-300"></i>
                    </button>
                    {% if repository.is_synced and repository.github_repo_url %}
                    <a href="{{ repository.github_repo_url }}" target="_blank" class="flex items-center text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
                        <i class="fab fa-github mr-1"></i>
                        <span class="hidden sm:inline">GitHub</span>
                    </a>
                    {% endif %}
                    {% if current_user %}
                    <a href="/dashboard" class="flex items-center text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400">
                        <i class="far fa-arrow-left mr-1"></i>
                        <span class="hidden sm:inline">Dashboard</span>
                    </a>
                    {% else %}
                    <a href="/" class="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                        <i class="far fa-sign-in-alt mr-1"></i>
                        <span class="hidden sm:inline">Sign In</span>
                    </a>
                    {% endif %}
                </div>
            </div>
            {% if repository.description %}
            <div class="mt-2 text-sm text-gray-600 dark:text-gray-400">
                {{ repository.description }}
            </div>
            {% endif %}
            <div class="mt-2 text-sm text-gray-600 dark:text-gray-400 italic">
                Last updated on {{ now.strftime('%Y-%m-%d') }}
            </div>
        </div>
    </div>

    <main class="container mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <!-- Error message -->
        <div id="errorContainer" class="hidden"></div>

        {% if error %}
        <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="far fa-exclamation-circle text-red-400 dark:text-red-500"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800 dark:text-red-200">{{ error }}</h3>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="bg-white dark:bg-custom-surface rounded-lg shadow transition-colors duration-200">
            <div class="p-4 sm:p-6 border-b border-gray-200 dark:border-dark-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <!-- Breadcrumb -->
                <div class="breadcrumb-container flex items-center text-sm">
                    <a href="/{{ profile_user.username }}/{{ repository.name }}?path=" class="text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400">
                        <i class="far fa-home"></i>
                    </a>
                    {% for item in breadcrumbs %}
                        <span class="mx-2 text-gray-500 dark:text-gray-400">/</span>
                        <a href="/{{ profile_user.username }}/{{ repository.name }}?path={{ item.path }}" class="text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400">
                            {{ item.name }}
                        </a>
                    {% endfor %}
                </div>

                <!-- Action Buttons - Only show if user owns the repository -->
                {% if current_user and current_user.id == profile_user.id %}
                <div class="flex space-x-2">
                    <a href="/fs/?path={{ current_path }}" class="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-150 flex items-center text-sm font-medium">
                        <i class="far fa-edit mr-2"></i>
                        <span class="hidden sm:inline">Manage Files</span>
                        <span class="sm:hidden">Edit</span>
                    </a>
                </div>
                {% endif %}
            </div>

            <!-- File Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-dark-700">
                    <thead class="bg-gray-50 dark:bg-custom-surface">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Name
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden sm:table-cell">
                                Last Modified
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider hidden md:table-cell">
                                Size
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-custom-surface divide-y divide-gray-200 dark:divide-dark-700">
                        {% for item in contents %}
                        <tr class="hover:bg-gray-50 dark:hover:bg-dark-800 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <i class="far {% if item.type == 'folder' %}fa-folder text-blue-500{% else %}fa-file text-gray-500 dark:text-gray-400{% endif %} mr-2"></i>
                                    {% if item.type == 'folder' %}
                                    <a href="/{{ profile_user.username }}/{{ repository.name }}?path={{ item.path }}" class="text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 hover:underline text-sm font-medium">{{ item.name }}</a>
                                    {% else %}
                                    <span class="text-gray-900 dark:text-gray-100 text-sm font-medium">{{ item.name }}</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 hidden sm:table-cell">
                                {{ item.updated_at.strftime('%Y-%m-%d %H:%M:%S') if item.updated_at else '-' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400 hidden md:table-cell">
                                {% if item.size is not none %}
                                    {{ format_file_size(item.size) }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}

                        {% if not contents %}
                        <tr>
                            <td colspan="3" class="px-6 py-10 text-center text-sm text-gray-500 dark:text-gray-400">
                                This folder is empty
                            </td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <script>
        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        const html = document.documentElement;

        // Check for saved dark mode preference
        if (localStorage.getItem('darkMode') === 'true' ||
            (!localStorage.getItem('darkMode') && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            html.classList.add('dark');
        }

        darkModeToggle.addEventListener('click', () => {
            html.classList.toggle('dark');
            localStorage.setItem('darkMode', html.classList.contains('dark'));
        });
    </script>
</body>
</html>
