{% extends "base.html" %}

{% block title %}{{ profile_user.username }}/{{ repository.name }} - WastedTime{% endblock %}

{% block content %}
<!-- Repository Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1>
            <i class="fas fa-code-branch"></i> 
            <a href="/{{ profile_user.username }}" class="text-decoration-none">{{ profile_user.username }}</a>
            <span class="text-muted">/</span>
            <strong>{{ repository.name }}</strong>
        </h1>
        {% if repository.description %}
        <p class="text-muted">{{ repository.description }}</p>
        {% endif %}
    </div>
    
    <div class="d-flex gap-2">
        {% if repository.is_synced and repository.github_repo_url %}
        <a href="{{ repository.github_repo_url }}" target="_blank" class="btn btn-outline-primary">
            <i class="fab fa-github"></i> View on GitHub
        </a>
        {% endif %}
        
        {% if current_user %}
        <a href="/dashboard" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Dashboard
        </a>
        {% else %}
        <a href="/" class="btn btn-primary">
            <i class="fas fa-sign-in-alt"></i> Sign In
        </a>
        {% endif %}
    </div>
</div>

<!-- Repository Info Bar -->
<div class="card mb-4">
    <div class="card-body py-2">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex gap-4">
                <span>
                    <i class="fas fa-user"></i> 
                    <a href="/{{ profile_user.username }}" class="text-decoration-none">{{ profile_user.display_name }}</a>
                </span>
                <span>
                    <i class="fas fa-calendar"></i> 
                    Created {{ repository.created_at.strftime('%B %d, %Y') }}
                </span>
                {% if repository.is_synced %}
                <span class="badge bg-success">
                    <i class="fab fa-github"></i> GitHub Synced
                </span>
                {% else %}
                <span class="badge bg-secondary">
                    <i class="fas fa-hdd"></i> Local Repository
                </span>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if error %}
<div class="alert alert-danger">
    <i class="fas fa-exclamation-triangle"></i> {{ error }}
</div>
{% endif %}

<!-- File Explorer -->
<div class="file-explorer">
    <!-- Breadcrumb Navigation -->
    <nav>
        <ol class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
            <li class="breadcrumb-item">
                {% if loop.last %}
                {{ breadcrumb.name }}
                {% else %}
                <a href="/{{ profile_user.username }}/{{ repository.name }}?path={{ breadcrumb.path }}">{{ breadcrumb.name }}</a>
                {% endif %}
            </li>
            {% endfor %}
        </ol>
    </nav>
    
    <!-- File List -->
    {% if contents %}
    <div class="card">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th style="width: 50px;"></th>
                        <th>Name</th>
                        <th style="width: 120px;">Size</th>
                        <th style="width: 180px;">Last Modified</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in contents %}
                    <tr>
                        <td>
                            <i class="{{ get_file_icon(item.mime_type, item.name.split('.')[-1] if '.' in item.name else None) if item.type == 'file' else 'fas fa-folder text-primary' }}"></i>
                        </td>
                        <td>
                            {% if item.type == 'folder' %}
                            <a href="/{{ profile_user.username }}/{{ repository.name }}?path={{ item.path }}" class="text-decoration-none">
                                {{ item.name }}
                            </a>
                            {% else %}
                            <span>{{ item.name }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if item.type == 'file' and item.size %}
                            {{ format_file_size(item.size) }}
                            {% else %}
                            -
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ item.updated_at.strftime('%b %d, %Y') if item.updated_at else '-' }}
                            </small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
            <h5>Empty Directory</h5>
            <p class="text-muted">This directory is empty.</p>
            {% if current_user and current_user.id == profile_user.id %}
            <p class="text-muted">
                <a href="/fs/?path={{ current_path }}" class="btn btn-primary">
                    <i class="fas fa-upload"></i> Manage Files
                </a>
            </p>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Repository Stats -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-info-circle"></i> Repository Information
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h5 class="text-primary">{{ repository.folders|length }}</h5>
                        <small class="text-muted">Folders</small>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-success">
                            {% set file_count = 0 %}
                            {% for folder in repository.folders %}
                                {% set file_count = file_count + folder.files|length %}
                            {% endfor %}
                            {{ file_count }}
                        </h5>
                        <small class="text-muted">Files</small>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-info">
                            {{ repository.created_at.strftime('%b %Y') }}
                        </h5>
                        <small class="text-muted">Created</small>
                    </div>
                    <div class="col-md-3">
                        <h5 class="text-warning">
                            {{ repository.updated_at.strftime('%b %Y') }}
                        </h5>
                        <small class="text-muted">Updated</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
