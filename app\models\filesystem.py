from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Text
from sqlalchemy.orm import relationship
from .base import BaseModel

class Folder(BaseModel):
    """Folder model for virtual file system."""

    __tablename__ = 'folders'

    name = Column(String(255), nullable=False)
    parent_id = Column(Integer, ForeignKey('folders.id'), nullable=True)
    repository_id = Column(Integer, ForeignKey('repositories.id'), nullable=True)

    # Relationships
    parent = relationship("Folder", remote_side=lambda: Folder.id, backref="children")
    repository = relationship("Repository", back_populates="folders")
    files = relationship("File", back_populates="folder", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Folder(id={self.id}, name='{self.name}', parent_id={self.parent_id})>"
    
    def get_full_path(self):
        """Get the full path from root to this folder."""
        if self.parent_id is None:
            return "/"
        
        path_parts = []
        current = self
        while current.parent_id is not None:
            path_parts.append(current.name)
            current = current.parent
        
        path_parts.reverse()
        return "/" + "/".join(path_parts) + "/"
    
    def is_root(self):
        """Check if this is a root folder."""
        return self.parent_id is None

class File(BaseModel):
    """File model for virtual file system."""
    
    __tablename__ = 'files'
    
    name = Column(String(255), nullable=False)
    path = Column(Text, nullable=True)  # Full virtual path
    folder_id = Column(Integer, ForeignKey('folders.id'), nullable=False)
    mime_type = Column(String(100), nullable=True)
    size = Column(Integer, nullable=True)
    storage_path = Column(String(500), nullable=True)  # Path to actual file in storage
    
    # Relationships
    folder = relationship("Folder", back_populates="files")
    
    def __repr__(self):
        return f"<File(id={self.id}, name='{self.name}', folder_id={self.folder_id})>"
    
    def get_full_path(self):
        """Get the full path including filename."""
        if not self.folder:
            return f"/{self.name}"
        
        folder_path = self.folder.get_full_path()
        if folder_path == "/":
            return f"/{self.name}"
        return f"{folder_path.rstrip('/')}/{self.name}"
    
    @property
    def extension(self):
        """Get file extension."""
        if '.' in self.name:
            return self.name.rsplit('.', 1)[1].lower()
        return None
