{% extends "base.html" %}

{% block title %}{{ profile_user.display_name }} (@{{ profile_user.username }}) - WastedTime{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-user"></i> {{ profile_user.display_name }}
        <small class="text-muted">@{{ profile_user.username }}</small>
    </h1>
    {% if current_user %}
    <a href="/dashboard" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Dashboard
    </a>
    {% else %}
    <a href="/" class="btn btn-primary">
        <i class="fas fa-sign-in-alt"></i> Sign In
    </a>
    {% endif %}
</div>

<!-- User Information -->
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                {% if profile_user.avatar_url %}
                <img src="{{ profile_user.avatar_url }}" alt="{{ profile_user.display_name }}" 
                     class="rounded-circle mb-3" style="width: 120px; height: 120px;">
                {% else %}
                <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                     style="width: 120px; height: 120px;">
                    <i class="fas fa-user fa-3x text-white"></i>
                </div>
                {% endif %}
                
                <h4>{{ profile_user.display_name }}</h4>
                <p class="text-muted">@{{ profile_user.username }}</p>
                
                {% if profile_user.email %}
                <p><i class="fas fa-envelope"></i> {{ profile_user.email }}</p>
                {% endif %}
                
                <p class="text-muted">
                    <small>Member since {{ profile_user.created_at.strftime('%B %Y') }}</small>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <!-- Repository Information -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-code-branch"></i> Repository
            </div>
            <div class="card-body">
                {% if repository %}
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5>
                            <a href="/{{ profile_user.username }}/{{ repository.name }}" class="text-decoration-none">
                                {{ repository.name }}
                            </a>
                        </h5>
                        {% if repository.description %}
                        <p class="text-muted">{{ repository.description }}</p>
                        {% endif %}
                        
                        <p class="mb-2">
                            <small class="text-muted">
                                Created {{ repository.created_at.strftime('%B %d, %Y') }}
                            </small>
                        </p>
                        
                        {% if repository.is_synced %}
                        <span class="badge bg-success">
                            <i class="fab fa-github"></i> Synced with GitHub
                        </span>
                        {% if repository.github_repo_url %}
                        <a href="{{ repository.github_repo_url }}" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                            <i class="fab fa-github"></i> View on GitHub
                        </a>
                        {% endif %}
                        {% else %}
                        <span class="badge bg-secondary">
                            <i class="fas fa-folder"></i> Local Repository
                        </span>
                        {% endif %}
                    </div>
                    
                    <div>
                        <a href="/{{ profile_user.username }}/{{ repository.name }}" class="btn btn-primary">
                            <i class="fas fa-folder-open"></i> Browse Files
                        </a>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-code-branch fa-3x text-muted mb-3"></i>
                    <h5>No Repository</h5>
                    <p class="text-muted">{{ profile_user.display_name }} hasn't created a repository yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Repository Stats (if repository exists) -->
        {% if repository %}
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-chart-bar"></i> Repository Stats
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="border-end">
                            <h4 class="text-primary">{{ repository.folders|length }}</h4>
                            <small class="text-muted">Folders</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border-end">
                            <h4 class="text-success">
                                {% set file_count = 0 %}
                                {% for folder in repository.folders %}
                                    {% set file_count = file_count + folder.files|length %}
                                {% endfor %}
                                {{ file_count }}
                            </h4>
                            <small class="text-muted">Files</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <h4 class="text-info">
                            {% if repository.is_synced %}
                            <i class="fab fa-github"></i>
                            {% else %}
                            <i class="fas fa-hdd"></i>
                            {% endif %}
                        </h4>
                        <small class="text-muted">
                            {% if repository.is_synced %}GitHub Synced{% else %}Local Only{% endif %}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
