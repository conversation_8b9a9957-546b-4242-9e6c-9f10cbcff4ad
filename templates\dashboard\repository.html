{% extends "base.html" %}

{% block title %}Repository Settings - WastedTime{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Repository Settings</h1>
    <a href="/dashboard" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
</div>

{% if repository %}
<!-- Existing Repository -->
<div class="card">
    <div class="card-header">
        <i class="fas fa-code-branch"></i> Repository Information
    </div>
    <div class="card-body">
        <div class="row" style="display: flex; gap: 2rem;">
            <div style="flex: 1;">
                <h5>
                    <a href="/{{ user.username }}/{{ repository.name }}" class="text-decoration-none">
                        {{ repository.name }}
                    </a>
                </h5>
                {% if repository.description %}
                <p class="text-muted">{{ repository.description }}</p>
                {% endif %}

                <p><strong>Public URL:</strong>
                    <code>/{{ user.username }}/{{ repository.name }}</code>
                    <button class="btn btn-sm btn-outline-secondary ms-1"
                            onclick="copyToClipboard('{{ request.url.scheme }}://{{ request.url.netloc }}/{{ user.username }}/{{ repository.name }}')">
                        <i class="fas fa-copy"></i>
                    </button>
                </p>
                
                <p><strong>Created:</strong> {{ repository.created_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                <p><strong>Last Updated:</strong> {{ repository.updated_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
                
                {% if repository.is_synced %}
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <strong>Synced with GitHub</strong>
                    <br>
                    <strong>GitHub Repository:</strong> 
                    <a href="{{ repository.github_repo_url }}" target="_blank">
                        {{ repository.full_name }}
                    </a>
                </div>
                {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> <strong>Not synced with GitHub</strong>
                    <br>
                    <small>
                        Sync your repository to push files to GitHub.<br>
                        <strong>Note:</strong> GitHub sync currently requires repository permissions.
                        This feature will be enhanced in future updates to request permissions only when needed.
                    </small>
                </div>
                {% endif %}
            </div>
            
            <div style="flex: 1;">
                <h6>Actions</h6>
                <div class="d-flex flex-column gap-2">
                    <a href="/{{ user.username }}/{{ repository.name }}" class="btn btn-primary">
                        <i class="fas fa-folder-open"></i> Browse Repository
                    </a>
                    
                    {% if not repository.is_synced %}
                    <button class="btn btn-success" onclick="syncWithGitHub()">
                        <i class="fab fa-github"></i> Sync with GitHub
                    </button>
                    {% endif %}
                    
                    <button class="btn btn-danger" onclick="confirmDeleteRepository()">
                        <i class="fas fa-trash"></i> Delete Repository
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<!-- Create New Repository -->
<div class="card">
    <div class="card-header">
        <i class="fas fa-plus-circle"></i> Create Repository
    </div>
    <div class="card-body">
        <p class="text-muted mb-4">
            You can create exactly one virtual repository. This repository will store your virtual files 
            and can be synced with GitHub.
        </p>
        
        <form id="createRepositoryForm">
            <div class="form-group">
                <label for="repositoryName" class="form-label">Repository Name *</label>
                <input type="text" id="repositoryName" name="name" class="form-control" 
                       placeholder="my-awesome-project" required>
                <small class="text-muted">
                    Choose a name for your repository. This will be used when syncing with GitHub.
                </small>
            </div>
            
            <div class="form-group">
                <label for="repositoryDescription" class="form-label">Description</label>
                <textarea id="repositoryDescription" name="description" class="form-control" 
                          rows="3" placeholder="A brief description of your project..."></textarea>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create Repository
            </button>
        </form>
    </div>
</div>
{% endif %}

<!-- Delete Confirmation Modal -->
<div id="deleteModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; min-width: 400px;">
        <h4 class="text-danger">
            <i class="fas fa-exclamation-triangle"></i> Delete Repository
        </h4>
        <p>
            Are you sure you want to delete your repository? This action cannot be undone.
            All files and folders will be permanently deleted.
        </p>
        <div class="d-flex gap-2">
            <button class="btn btn-danger" onclick="deleteRepository()">
                <i class="fas fa-trash"></i> Delete
            </button>
            <button class="btn btn-secondary" onclick="hideDeleteModal()">Cancel</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Create repository form handler
{% if not repository %}
document.getElementById('createRepositoryForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    try {
        const response = await axios.post('/repo/create', formData);
        showAlert(response.data.message, 'success');
        
        // Redirect to dashboard after successful creation
        setTimeout(() => {
            window.location.href = '/dashboard';
        }, 2000);
    } catch (error) {
        handleApiError(error);
    }
});
{% endif %}

// Sync with GitHub
async function syncWithGitHub() {
    try {
        const response = await axios.post('/repo/sync-github');
        showAlert(response.data.message, 'success');
        
        // Reload page to show updated status
        setTimeout(() => {
            window.location.reload();
        }, 2000);
    } catch (error) {
        handleApiError(error);
    }
}

// Delete repository functions
function confirmDeleteRepository() {
    document.getElementById('deleteModal').style.display = 'block';
}

function hideDeleteModal() {
    document.getElementById('deleteModal').style.display = 'none';
}

async function deleteRepository() {
    try {
        const response = await axios.delete('/repo/');
        showAlert(response.data.message, 'success');
        
        // Redirect to dashboard after successful deletion
        setTimeout(() => {
            window.location.href = '/dashboard';
        }, 2000);
    } catch (error) {
        handleApiError(error);
    }
}

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('URL copied to clipboard!', 'success');
    }, function(err) {
        showAlert('Failed to copy URL', 'danger');
    });
}
</script>
{% endblock %}
