{% extends "base.html" %}

{% block title %}Dashboard - WastedTime{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Dashboard</h1>
    <div class="d-flex gap-2">
        {% if user.repository %}
        <a href="/fs/" class="btn btn-primary">
            <i class="fas fa-folder-open"></i> File Explorer
        </a>
        {% endif %}
        <a href="/repo/" class="btn btn-secondary">
            <i class="fas fa-cog"></i> Repository Settings
        </a>
    </div>
</div>

<div class="row" style="display: flex; gap: 2rem;">
    <!-- User Info Card -->
    <div class="card" style="flex: 1;">
        <div class="card-header">
            <i class="fas fa-user"></i> User Information
        </div>
        <div class="card-body">
            <div class="d-flex align-items-center mb-3">
                {% if user.avatar_url %}
                <img src="{{ user.avatar_url }}" alt="Avatar" class="avatar" style="width: 60px; height: 60px; margin-right: 1rem;">
                {% endif %}
                <div>
                    <h5 class="mb-1">{{ user.display_name }}</h5>
                    <p class="text-muted mb-0">@{{ user.username }}</p>
                    {% if user.email %}
                    <small class="text-muted">{{ user.email }}</small>
                    {% endif %}
                </div>
            </div>
            <p><strong>GitHub ID:</strong> {{ user.github_id }}</p>
            <p><strong>Member since:</strong> {{ user.created_at.strftime('%B %d, %Y') }}</p>
        </div>
    </div>

    <!-- Repository Info Card -->
    <div class="card" style="flex: 1;">
        <div class="card-header">
            <i class="fas fa-code-branch"></i> Repository Status
        </div>
        <div class="card-body">
            {% if user.repository %}
            <h5 class="text-success">
                <i class="fas fa-check-circle"></i> Repository Created
            </h5>
            <p><strong>Name:</strong>
                <a href="/{{ user.username }}/{{ user.repository.name }}" class="text-decoration-none">
                    {{ user.repository.name }}
                </a>
            </p>
            {% if user.repository.description %}
            <p><strong>Description:</strong> {{ user.repository.description }}</p>
            {% endif %}
            <p><strong>Public URL:</strong>
                <code>/{{ user.username }}/{{ user.repository.name }}</code>
                <button class="btn btn-sm btn-outline-secondary ms-1"
                        onclick="copyToClipboard('{{ request.url.scheme }}://{{ request.url.netloc }}/{{ user.username }}/{{ user.repository.name }}')">
                    <i class="fas fa-copy"></i>
                </button>
            </p>
            
            {% if user.repository.is_synced %}
            <div class="alert alert-success">
                <i class="fas fa-sync"></i> Synced with GitHub
                <br>
                <a href="{{ user.repository.github_repo_url }}" target="_blank" class="btn btn-sm btn-primary mt-2">
                    <i class="fab fa-github"></i> View on GitHub
                </a>
            </div>
            {% else %}
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> Not synced with GitHub
                <br>
                <small>Go to Repository Settings to sync with GitHub</small>
            </div>
            {% endif %}
            
            <p><strong>Created:</strong> {{ user.repository.created_at.strftime('%B %d, %Y') }}</p>
            {% else %}
            <div class="text-center">
                <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
                <h5>No Repository</h5>
                <p class="text-muted">You haven't created a repository yet.</p>
                <a href="/repo/" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Repository
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% if user.repository %}
<!-- Quick Actions -->
<div class="card mt-4">
    <div class="card-header">
        <i class="fas fa-bolt"></i> Quick Actions
    </div>
    <div class="card-body">
        <div class="d-flex gap-3">
            <a href="/fs/" class="btn btn-primary">
                <i class="fas fa-folder-open"></i> Browse Files
            </a>
            <a href="/{{ user.username }}/{{ user.repository.name }}" class="btn btn-outline-primary">
                <i class="fas fa-eye"></i> Public View
            </a>
            <button class="btn btn-success" onclick="showUploadModal()">
                <i class="fas fa-upload"></i> Upload File
            </button>
            <button class="btn btn-secondary" onclick="showCreateFolderModal()">
                <i class="fas fa-folder-plus"></i> Create Folder
            </button>
            {% if not user.repository.is_synced %}
            <a href="/repo/" class="btn btn-warning">
                <i class="fab fa-github"></i> Sync with GitHub
            </a>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

<!-- Upload Modal -->
<div id="uploadModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; min-width: 400px;">
        <h4>Upload File</h4>
        <form id="uploadForm">
            <div class="form-group">
                <label class="form-label">Select File:</label>
                <input type="file" id="fileInput" class="form-control" required>
            </div>
            <div class="form-group">
                <label class="form-label">Upload to:</label>
                <input type="text" id="uploadPath" class="form-control" value="/" placeholder="/">
            </div>
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Upload</button>
                <button type="button" class="btn btn-secondary" onclick="hideUploadModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>

<!-- Create Folder Modal -->
<div id="createFolderModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; min-width: 400px;">
        <h4>Create Folder</h4>
        <form id="createFolderForm">
            <div class="form-group">
                <label class="form-label">Folder Name:</label>
                <input type="text" id="folderName" class="form-control" required>
            </div>
            <div class="form-group">
                <label class="form-label">Create in:</label>
                <input type="text" id="folderPath" class="form-control" value="/" placeholder="/">
            </div>
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">Create</button>
                <button type="button" class="btn btn-secondary" onclick="hideCreateFolderModal()">Cancel</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showUploadModal() {
    document.getElementById('uploadModal').style.display = 'block';
}

function hideUploadModal() {
    document.getElementById('uploadModal').style.display = 'none';
    document.getElementById('uploadForm').reset();
}

function showCreateFolderModal() {
    document.getElementById('createFolderModal').style.display = 'block';
}

function hideCreateFolderModal() {
    document.getElementById('createFolderModal').style.display = 'none';
    document.getElementById('createFolderForm').reset();
}

// Upload form handler
document.getElementById('uploadForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const fileInput = document.getElementById('fileInput');
    const pathInput = document.getElementById('uploadPath');
    
    if (!fileInput.files[0]) {
        showAlert('Please select a file', 'danger');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('path', pathInput.value || '/');
    
    try {
        const response = await axios.post('/fs/upload-file', formData);
        showAlert(response.data.message, 'success');
        hideUploadModal();
    } catch (error) {
        handleApiError(error);
    }
});

// Create folder form handler
document.getElementById('createFolderForm').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const nameInput = document.getElementById('folderName');
    const pathInput = document.getElementById('folderPath');
    
    const formData = new FormData();
    formData.append('name', nameInput.value);
    formData.append('path', pathInput.value || '/');
    
    try {
        const response = await axios.post('/fs/create-folder', formData);
        showAlert(response.data.message, 'success');
        hideCreateFolderModal();
    } catch (error) {
        handleApiError(error);
    }
});

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('URL copied to clipboard!', 'success');
    }, function(err) {
        showAlert('Failed to copy URL', 'danger');
    });
}
</script>
{% endblock %}
