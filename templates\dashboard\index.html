{% extends "base.html" %}

{% block title %}Dashboard - WastedTime{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Dashboard</h1>
    <div class="d-flex gap-2">
        <a href="/repo/" class="btn btn-secondary">
            <i class="fas fa-cog"></i> Repository Settings
        </a>
    </div>
</div>

<div class="row" style="display: flex; gap: 2rem;">
    <!-- User Info Card -->
    <div class="card" style="flex: 1;">
        <div class="card-header">
            <i class="fas fa-user"></i> User Information
        </div>
        <div class="card-body">
            <div class="d-flex align-items-center mb-3">
                {% if user.avatar_url %}
                <img src="{{ user.avatar_url }}" alt="Avatar" class="avatar" style="width: 60px; height: 60px; margin-right: 1rem;">
                {% endif %}
                <div>
                    <h5 class="mb-1">{{ user.display_name }}</h5>
                    <p class="text-muted mb-0">@{{ user.username }}</p>
                    {% if user.email %}
                    <small class="text-muted">{{ user.email }}</small>
                    {% endif %}
                </div>
            </div>
            <p><strong>GitHub ID:</strong> {{ user.github_id }}</p>
            <p><strong>Member since:</strong> {{ user.created_at.strftime('%B %d, %Y') }}</p>
        </div>
    </div>

    <!-- Repository Info Card -->
    <div class="card" style="flex: 1;">
        <div class="card-header">
            <i class="fas fa-code-branch"></i> My Repository
        </div>
        <div class="card-body">
            {% if user.repository %}
            <div class="repository-card" style="cursor: pointer; padding: 1rem; border: 1px solid #e0e0e0; border-radius: 8px; transition: all 0.2s;"
                 onclick="window.location.href='/{{ user.username }}/{{ user.repository.name }}'"
                 onmouseover="this.style.backgroundColor='#f8f9fa'; this.style.borderColor='#007bff';"
                 onmouseout="this.style.backgroundColor=''; this.style.borderColor='#e0e0e0';">

                <div class="d-flex justify-content-between align-items-start">
                    <div style="flex: 1;">
                        <h5 class="mb-2">
                            <i class="fas fa-code-branch text-primary"></i>
                            {{ user.repository.name }}
                        </h5>
                        {% if user.repository.description %}
                        <p class="text-muted mb-2">{{ user.repository.description }}</p>
                        {% endif %}

                        <div class="d-flex gap-3 mb-2">
                            {% if user.repository.is_synced %}
                            <span class="badge bg-success">
                                <i class="fab fa-github"></i> GitHub Synced
                            </span>
                            {% else %}
                            <span class="badge bg-secondary">
                                <i class="fas fa-hdd"></i> Local Only
                            </span>
                            {% endif %}
                        </div>

                        <small class="text-muted">
                            Created {{ user.repository.created_at.strftime('%B %d, %Y') }}
                        </small>
                    </div>

                    <div class="text-end">
                        <i class="fas fa-arrow-right text-muted"></i>
                    </div>
                </div>

                <div class="mt-3 pt-2 border-top">
                    <small class="text-muted">
                        <i class="fas fa-link"></i>
                        /{{ user.username }}/{{ user.repository.name }}
                    </small>
                </div>
            </div>

            <div class="mt-3 d-flex gap-2">
                <button class="btn btn-sm btn-outline-secondary"
                        onclick="event.stopPropagation(); copyToClipboard('{{ request.url.scheme }}://{{ request.url.netloc }}/{{ user.username }}/{{ user.repository.name }}')">
                    <i class="fas fa-copy"></i> Copy URL
                </button>
                {% if user.repository.is_synced and user.repository.github_repo_url %}
                <a href="{{ user.repository.github_repo_url }}" target="_blank" class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation();">
                    <i class="fab fa-github"></i> GitHub
                </a>
                {% endif %}
            </div>
            {% else %}
            <div class="text-center">
                <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
                <h5>No Repository</h5>
                <p class="text-muted">You haven't created a repository yet.</p>
                <a href="/repo/" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Repository
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>




{% endblock %}

{% block extra_js %}
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('URL copied to clipboard!', 'success');
    }, function(err) {
        showAlert('Failed to copy URL', 'danger');
    });
}
</script>
{% endblock %}
