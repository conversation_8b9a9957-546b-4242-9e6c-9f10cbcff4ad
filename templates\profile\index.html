{% extends "base.html" %}

{% block title %}{{ user.display_name }} - Profile - WastedTime{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>
        <i class="fas fa-user"></i> My Profile
    </h1>
    <a href="/dashboard" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
</div>

<!-- User Information -->
<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                {% if user.avatar_url %}
                <img src="{{ user.avatar_url }}" alt="{{ user.display_name }}" 
                     class="rounded-circle mb-3" style="width: 120px; height: 120px;">
                {% else %}
                <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                     style="width: 120px; height: 120px;">
                    <i class="fas fa-user fa-3x text-white"></i>
                </div>
                {% endif %}
                
                <h4>{{ user.display_name }}</h4>
                <p class="text-muted">@{{ user.username }}</p>
                
                {% if user.email %}
                <p><i class="fas fa-envelope"></i> {{ user.email }}</p>
                {% endif %}
                
                <p class="text-muted">
                    <small>Member since {{ user.created_at.strftime('%B %Y') }}</small>
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <!-- Repository Information -->
        <div class="card">
            <div class="card-header">
                <i class="fas fa-code-branch"></i> Repository
            </div>
            <div class="card-body">
                {% if repository %}
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5>
                            <a href="/{{ user.username }}/{{ repository.name }}" class="text-decoration-none">
                                {{ repository.name }}
                            </a>
                        </h5>
                        {% if repository.description %}
                        <p class="text-muted">{{ repository.description }}</p>
                        {% endif %}
                        
                        <p class="mb-2">
                            <small class="text-muted">
                                Created {{ repository.created_at.strftime('%B %d, %Y') }}
                            </small>
                        </p>
                        
                        {% if repository.is_synced %}
                        <span class="badge bg-success">
                            <i class="fab fa-github"></i> Synced with GitHub
                        </span>
                        {% else %}
                        <span class="badge bg-warning">
                            <i class="fas fa-exclamation-triangle"></i> Not synced
                        </span>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex flex-column gap-2">
                        <a href="/{{ user.username }}/{{ repository.name }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-folder-open"></i> Browse Files
                        </a>
                        <a href="/repo/" class="btn btn-secondary btn-sm">
                            <i class="fas fa-cog"></i> Settings
                        </a>
                    </div>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-code-branch fa-3x text-muted mb-3"></i>
                    <h5>No Repository</h5>
                    <p class="text-muted">You haven't created a repository yet.</p>
                    <a href="/repo/" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create Repository
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Quick Links -->
        <div class="card mt-4">
            <div class="card-header">
                <i class="fas fa-link"></i> Quick Links
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Your Profile</h6>
                        <div class="mb-3">
                            <code>{{ request.url.scheme }}://{{ request.url.netloc }}/{{ user.username }}</code>
                            <button class="btn btn-sm btn-outline-secondary ms-2" 
                                    onclick="copyToClipboard('{{ request.url.scheme }}://{{ request.url.netloc }}/{{ user.username }}')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    
                    {% if repository %}
                    <div class="col-md-6">
                        <h6>Your Repository</h6>
                        <div class="mb-3">
                            <code>{{ request.url.scheme }}://{{ request.url.netloc }}/{{ user.username }}/{{ repository.name }}</code>
                            <button class="btn btn-sm btn-outline-secondary ms-2" 
                                    onclick="copyToClipboard('{{ request.url.scheme }}://{{ request.url.netloc }}/{{ user.username }}/{{ repository.name }}')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        showAlert('URL copied to clipboard!', 'success');
    }, function(err) {
        showAlert('Failed to copy URL', 'danger');
    });
}
</script>
{% endblock %}
